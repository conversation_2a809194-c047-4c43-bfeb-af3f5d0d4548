package app

import (
	"context"
	"fmt"
	"time"

	"billing_service/model"

	redis "github.com/redis/go-redis/v9"
)

func (a *App) RefillDriverBalance(ctx context.Context, driverId, amount, amountType, accountId int, comment, reqId string) (err error) {
	exists, err := a.setRequestID(ctx, reqId)
	if err != nil || exists {
		return
	}

	if amountType == 0 {
		amountType = a.cfg.Defaults.PaymePaymentTypeFromMytaxiOutsourceBilling
	}

	if accountId == 0 {
		accountId = a.cfg.Defaults.BillingPaycomAccountNum
	}

	balance, err := a.repo.RefillDriverBalance(ctx, driverId, amount, amountType, accountId, comment)
	if err != nil {
		_ = a.redis.Del(ctx, reqId)
		return
	}

	if balance >= a.repo.GetSysParam("driver_min_balance_for_blocking", "0").Int() {
		err = a.unblockDriverForRefillBalance(ctx, driverId)
		if err != nil {
			a.log.Errorf("unblock driver %d: %v", driverId, err)
			err = nil // TODO make async
		}
	}

	return
}

func (a *App) RefineDriverBalance(ctx context.Context, driverId, amount, account int, comment string, reqId string) (err error) {
	exists, err := a.setRequestID(ctx, reqId)
	if err != nil || exists {
		return
	}

	if account == 0 {
		account = a.cfg.Defaults.BillingDeductionAccountNum
	}

	balance, err := a.repo.RefineDriverBalance(ctx, driverId, amount, account, comment)
	if err != nil {
		_ = a.redis.Del(ctx, reqId)
		return err
	}

	if balance < a.repo.GetSysParam("driver_min_balance_for_blocking", "0").Int() {
		err = a.blockDriverForLowBalance(ctx, driverId)
		if err != nil {
			a.log.Errorf("block driver %d: %v", err)
			err = nil // TODO make async
		}
	}

	return
}

func (a *App) setRequestID(ctx context.Context, reqId string) (exists bool, err error) {
	val, err := a.redis.SetArgs(ctx, reqId, 1, redis.SetArgs{Get: true, TTL: 10 * time.Minute, Mode: "NX"}).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		} else {
			err = fmt.Errorf("redis get and set: %v", err)
		}
		return
	}

	if val != "" {
		exists = true
	}

	return
}

func (a *App) checkAndBlockDriverForLowBalance(ctx context.Context, driverId int) (err error) {
	balance, err := a.repo.GetDriverBalance(ctx, driverId)
	if balance.Balance >= a.repo.GetSysParam("driver_min_balance_for_blocking", "-10000").Int() {
		return
	}
	success, err := a.repo.BlockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: false})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:blocked", driverId), "driver_blocked_for_low_balance")
	}
	return
}

func (a *App) blockDriverForLowBalance(ctx context.Context, driverId int) (err error) {
	success, err := a.repo.BlockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: false})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:blocked", driverId), "driver_blocked_for_low_balance")
	}
	return
}

func (a *App) unblockDriverForRefillBalance(ctx context.Context, driverId int) (err error) {
	success, err := a.repo.UnblockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: true})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:unblocked", driverId), "driver_unblocked_for_refill_balance")
	}
	return
}

func (a *App) DriverTax(ctx context.Context, orderId, driverId, amount int, comment string) (err error) {
	_, err = a.repo.DriverTax(ctx, orderId, driverId, amount, comment)
	return
}
