package api

import (
	"billing_service/model"
	"context"
	"fmt"
)

func (h *handler) subscribeToEvents() {
	err := h.nats.Subscribe("orders.finished.*", "billing_service:orders:finished", h.orderFinished)
	if err != nil {
		panic(err)
	}

	err = h.nats.Subscribe("drivers.bonus", "billing_service:drivers:bonus", h.driverBonus)
	if err != nil {
		panic(err)
	}

	err = h.nats.Subscribe("orders.fiscal_limit", "billing_service:orders:fiscal_limit", h.ordersFiscalLimit)
	if err != nil {
		panic(err)
	}

}

func (h *handler) orderFinished(payload []byte) (err error) {
	var event model.FinishedOrderEvent

	err = json.Unmarshal(payload, &event)
	if err != nil {
		h.log.Error("order event: unmarshal message: ", err)
		return
	}

	req := model.PayOrderRequest{
		OrderId:            event.OrderId,
		ClientId:           event.ClientId,
		DriverId:           event.DriverId,
		InitiatorId:        event.InitiatorId,
		PaymentTypeId:      event.PaymentTypeId,
		CorpId:             event.CorpId,
		TotalCost:          event.Cost.Total,
		TotalClientCost:    event.Cost.TotalClient,
		PromoCost:          event.Cost.Promo,
		CashbackCost:       event.Cost.PaidFromBonus,
		DiscountCost:       event.Cost.Discount,
		TariffDiscountCost: event.Cost.TariffDiscount,
		Commission:         event.Commission,
		PartnerCommission:  event.PartnerCommission,
		InsuranceAmount:    event.Cost.InsuranceAmount,
	}

	err = h.app.PayOrder(req)
	if err != nil {
		h.log.Errorf("pay order %d failed: %v", req.OrderId, err)
	}

	return
}

func (h *handler) driverBonus(payload []byte) (err error) {
	var event model.DriverBonusEvent

	err = json.Unmarshal(payload, &event)
	if err != nil {
		h.log.Error("driver bonus event: unmarshal message: ", err)
		err = nil
		return
	}

	if event.DriverId <= 0 {
		h.log.Error("driver bonus event: invalid driver_id: ", event.DriverId)
		return
	}

	if event.BonusAmount <= 0 {
		h.log.Error("driver bonus event: invalid bonus_amount: ", event.BonusAmount)
		return
	}

	if event.ChallengeId <= 0 {
		h.log.Error("driver bonus event: invalid challenge_id: ", event.ChallengeId)
		return
	}

	err = h.app.ProcessDriverBonus(context.Background(), event)
	if err != nil {
		return
	}

	h.log.Infof("driver %d bonus processed successfully: %d tiyin", event.DriverId, event.BonusAmount)
	return
}

func (h *handler) ordersFiscalLimit(payload []byte) (err error) {
	var event model.FiscalLimitEvent

	err = json.Unmarshal(payload, &event)
	if err != nil {
		h.log.Error("fiscal limit event: unmarshal message: ", err)
		err = nil
		return
	}

	if event.OrderId <= 0 {
		h.log.Error("fiscal limit event: invalid order_id: ", event.OrderId)
		return
	}

	if event.DriverId <= 0 {
		h.log.Error("fiscal limit event: invalid driver_id: ", event.DriverId)
		return
	}

	if event.Amount <= 0 {
		h.log.Error("fiscal limit event: invalid amount: ", event.Amount)
		return
	}

	h.log.Infof("fiscal limit event received - order_id: %d, driver_id: %d, amount: %d",
		event.OrderId, event.DriverId, event.Amount)

	comment := fmt.Sprintf("Налоговый сбор с водителя %d", event.OrderId)
	err = h.app.DriverTax(context.Background(), event.OrderId, event.DriverId, event.Amount, comment)
	if err != nil {
		h.log.Errorf("failed to process driver tax for fiscal limit - order_id: %d, driver_id: %d, amount: %d, error: %v",
			event.OrderId, event.DriverId, event.Amount, err)
		return
	}

	h.log.Infof("driver tax processed successfully for fiscal limit - order_id: %d, driver_id: %d, amount: %d",
		event.OrderId, event.DriverId, event.Amount)

	return
}
